/**
 * Custom hooks for catalog management operations
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { CatalogService } from '@/services/api/catalogService';
import {
  CreateCategoryRequest,
  UpdateCategoryRequest,
  CreateSubcategoryRequest,
  UpdateSubcategoryRequest,
  CreateProductRequest,
  UpdateProductRequest
} from '@/types/catalog.types';
import { QueryParams } from '@/types/api/common';

/**
 * Hook for fetching a paginated list of categories
 * @param params Query parameters for pagination, sorting, and filtering
 * @returns Query result with categories data, loading state, and error state
 */
export const useCategories = (params?: QueryParams) => {
  return useQuery({
    queryKey: ['categories', params],
    queryFn: () => CatalogService.getCategories(params),
  });
};

/**
 * Hook for fetching a single category by ID
 * @param id Category ID
 * @returns Query result with category data, loading state, and error state
 */
export const useCategory = (id: number) => {
  return useQuery({
    queryKey: ['category', id],
    queryFn: () => CatalogService.getCategoryById(id),
    enabled: !!id, // Only run the query if id is provided
  });
};

/**
 * Hook for creating a new category
 * @returns Mutation for creating a category with loading, error, and success states
 */
export const useCreateCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateCategoryRequest) => CatalogService.createCategory(data),
    onSuccess: () => {
      // Invalidate the categories query to refetch the data
      queryClient.invalidateQueries({ queryKey: ['categories'] });
    },
  });
};

/**
 * Hook for updating an existing category
 * @returns Mutation for updating a category with loading, error, and success states
 */
export const useUpdateCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateCategoryRequest }) =>
      CatalogService.updateCategory(id, data),
    onSuccess: (_, variables) => {
      // Invalidate the specific category query and the categories list
      queryClient.invalidateQueries({ queryKey: ['category', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['categories'] });
    },
  });
};

/**
 * Hook for deleting a category
 * @returns Mutation for deleting a category with loading, error, and success states
 */
export const useDeleteCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => CatalogService.deleteCategory(id),
    onSuccess: () => {
      // Invalidate the categories query to refetch the data
      queryClient.invalidateQueries({ queryKey: ['categories'] });
    },
  });
};

/**
 * Hook for fetching a paginated list of subcategories
 * @param params Query parameters for pagination, sorting, and filtering
 * @returns Query result with subcategories data, loading state, and error state
 */
export const useSubcategories = (params?: QueryParams) => {
  // Extract category_id from params for better query key structure
  const categoryId = params?.filter?.category_id;

  return useQuery({
    queryKey: ['subcategories', 'all', { categoryId, ...params }],
    queryFn: () => CatalogService.getSubcategories(params),
  });
};

/**
 * Hook for fetching subcategories by category ID
 * @param categoryId Category ID
 * @param params Query parameters for pagination, sorting, and filtering
 * @returns Query result with subcategories data, loading state, and error state
 */
export const useSubcategoriesByCategory = (categoryId: number, params?: QueryParams) => {
  return useQuery({
    queryKey: ['subcategories', categoryId, params],
    queryFn: () => CatalogService.getSubcategoriesByCategoryId(categoryId, params),
    enabled: !!categoryId, // Only run the query if categoryId is provided
  });
};

/**
 * Hook for fetching a single subcategory by ID
 * @param categoryId Category ID
 * @param id Subcategory ID
 * @returns Query result with subcategory data, loading state, and error state
 */
export const useSubcategory = (categoryId: number, id: number) => {
  return useQuery({
    queryKey: ['subcategory', categoryId, id],
    queryFn: () => CatalogService.getSubcategoryById(categoryId, id),
    enabled: !!(categoryId && id), // Only run the query if both IDs are provided
  });
};

/**
 * Hook for creating a new subcategory
 * @returns Mutation for creating a subcategory with loading, error, and success states
 */
export const useCreateSubcategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateSubcategoryRequest) => CatalogService.createSubcategory(data),
    onSuccess: (_, variables) => {
      // Invalidate the subcategories query and the subcategories by category query
      queryClient.invalidateQueries({ queryKey: ['subcategories'] });
      if (variables.categoryId) {
        queryClient.invalidateQueries({ queryKey: ['subcategories', variables.categoryId] });
      }
    },
  });
};

/**
 * Hook for updating an existing subcategory
 * @returns Mutation for updating a subcategory with loading, error, and success states
 */
export const useUpdateSubcategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ categoryId, id, data }: { categoryId: number; id: number; data: UpdateSubcategoryRequest }) =>
      CatalogService.updateSubcategory(categoryId, id, data),
    onSuccess: (_, variables) => {
      // Invalidate the specific subcategory query and the subcategories list
      queryClient.invalidateQueries({ queryKey: ['subcategory', variables.categoryId, variables.id] });
      queryClient.invalidateQueries({ queryKey: ['subcategories'] });
      queryClient.invalidateQueries({ queryKey: ['subcategories', variables.categoryId] });
    },
  });
};

/**
 * Hook for deleting a subcategory
 * @returns Mutation for deleting a subcategory with loading, error, and success states
 */
export const useDeleteSubcategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ categoryId, id }: { categoryId: number; id: number }) =>
      CatalogService.deleteSubcategory(categoryId, id),
    onSuccess: () => {
      // Invalidate the subcategories query to refetch the data
      queryClient.invalidateQueries({ queryKey: ['subcategories'] });
    },
  });
};

/**
 * Hook for fetching a paginated list of products
 * @param params Query parameters for pagination, sorting, and filtering
 * @returns Query result with products data, loading state, and error state
 */
export const useProducts = (params?: QueryParams) => {
  return useQuery({
    queryKey: ['products', params],
    queryFn: () => CatalogService.getProducts(params),
  });
};

/**
 * Hook for fetching a single product by ID
 * @param id Product ID
 * @returns Query result with product data, loading state, and error state
 */
export const useProduct = (id: number) => {
  return useQuery({
    queryKey: ['product', id],
    queryFn: () => CatalogService.getProductById(id),
    enabled: !!id, // Only run the query if id is provided
  });
};

/**
 * Hook for creating a new product
 * @returns Mutation for creating a product with loading, error, and success states
 */
export const useCreateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProductRequest) => CatalogService.createProduct(data),
    onSuccess: () => {
      // Invalidate the products query to refetch the data
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
};

/**
 * Hook for updating an existing product
 * @returns Mutation for updating a product with loading, error, and success states
 */
export const useUpdateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateProductRequest }) =>
      CatalogService.updateProduct(id, data),
    onSuccess: (_, variables) => {
      // Invalidate the specific product query and the products list
      queryClient.invalidateQueries({ queryKey: ['product', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
};

/**
 * Hook for deleting a product
 * @returns Mutation for deleting a product with loading, error, and success states
 */
export const useDeleteProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => CatalogService.deleteProduct(id),
    onSuccess: () => {
      // Invalidate the products query to refetch the data
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
};
