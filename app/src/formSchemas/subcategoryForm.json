{"fields": [{"type": "text", "name": "name", "label": "Subcategory Name", "placeholder": "Enter subcategory name", "icon": "pi-tag", "validation": {"required": true, "minLength": 2, "maxLength": 100}}, {"type": "text", "name": "description", "label": "Description", "placeholder": "Enter subcategory description", "icon": "pi-align-left", "validation": {"required": true, "maxLength": 500}}, {"type": "select", "name": "categoryId", "label": "Parent Category", "placeholder": "Select parent category", "icon": "pi-list", "options": [], "validation": {"required": true}}], "actions": [{"id": "submit", "type": "submit", "label": "Save Subcategory"}, {"id": "cancel", "type": "button", "label": "Cancel"}]}