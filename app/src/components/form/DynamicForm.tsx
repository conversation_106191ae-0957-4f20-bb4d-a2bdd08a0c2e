import { useMemo, useEffect } from 'react';
import { useF<PERSON>, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z, ZodTypeAny, ZodObject } from 'zod';
import { RHFInputText } from './RHFInputText';
import { RHFPassword } from './RHFPassword';
import { RHFCheckbox } from './RHFCheckbox';
import { RHFSelect } from './RHFSelect';
import { RHFNumber } from './RHFNumber';
import { RHFCalendar } from './RHFCalendar';
import { RHFImageUpload } from './RHFImageUpload';
import Button from '@/components/ui/Button/Button';
import './DynamicForm.css';

// Date field type is now supported
// Define interfaces for better type safety
interface ValidationRules {
  required?: boolean;
  email?: boolean;
  minLength?: number;  // For string length validation
  maxLength?: number;  // For string length validation
  min?: number;        // For number value validation
  max?: number;        // For number value validation
}

interface FieldSchema {
  name: string;
  type: 'text' | 'password' | 'checkbox' | 'number' | 'select' | 'date' | 'image';
  label?: string;
  validation?: ValidationRules;
  options?: string[] | { label: string; value: string }[]; // For select fields
  placeholder?: string;
  icon?: string;
  dateRestriction?: {
    range: 'past' | 'future' | 'all'; // Date range restriction: past, future, or all dates
    includeToday?: boolean; // Whether to include today in the range (default: false for past/future, true for all)
  };
  folder?: string; // For image fields - the storage folder
  maxSizeInMB?: number; // For image fields - max file size
  acceptedFileTypes?: string; // For image fields - accepted file types
}

interface ActionSchema {
  id: string;
  type: 'submit' | 'button'; // Restrict to supported button types
  label: string;
}

interface FormSchema {
  fields: FieldSchema[];
  actions: ActionSchema[];
}

// Type for button handlers
type ButtonHandler = (data?: any) => void;

interface DynamicFormProps {
  schema: FormSchema;
  onSubmit: (data: any) => void;
  defaultValues?: Record<string, any>;
  className?: string;
  buttonHandlers?: Record<string, ButtonHandler>;
  onFieldChange?: (field: string, value: any) => void;
}

const getZodSchema = (fields: FieldSchema[]): ZodObject<any> => {
  if (!Array.isArray(fields) || fields.length === 0) {
    throw new Error('Fields must be a non-empty array');
  }

  const shape: Record<string, ZodTypeAny> = {};

  fields.forEach((field) => {
    if (!field.name) {
      throw new Error('Field name is required');
    }

    let validator: ZodTypeAny;

    switch (field.type) {
      case 'text': {
        let stringValidator = z.string().trim();

        if (field.validation?.required) {
          stringValidator = stringValidator.min(1, `${field.label || field.name} is required`);
        }
        if (field.validation?.email) {
          stringValidator = stringValidator.email('Invalid email address');
        }
        if (field.validation?.minLength) {
          stringValidator = stringValidator.min(
            field.validation.minLength,
            `Minimum ${field.validation.minLength} characters`
          );
        }
        if (field.validation?.maxLength) {
          stringValidator = stringValidator.max(
            field.validation.maxLength,
            `Maximum ${field.validation.maxLength} characters`
          );
        }

        validator = stringValidator;
        break;
      }

      case 'password': {
        let stringValidator = z.string();

        if (field.validation?.required) {
          stringValidator = stringValidator.min(1, `${field.label || field.name} is required`);
        }
        if (field.validation?.minLength) {
          stringValidator = stringValidator.min(
            field.validation.minLength,
            `Minimum ${field.validation.minLength} characters`
          );
        }
        if (field.validation?.maxLength) {
          stringValidator = stringValidator.max(
            field.validation.maxLength,
            `Maximum ${field.validation.maxLength} characters`
          );
        }

        validator = stringValidator;
        break;
      }

      case 'checkbox':
        validator = z.boolean().optional();
        break;

      case 'number': {
        // For number fields, we'll use a string validator first (since form inputs are strings)
        // and then transform it to a number
        let baseValidator = z.string();

        // Add required validation if needed
        if (field.validation?.required) {
          baseValidator = baseValidator.min(1, `${field.label || field.name} is required`);
        }

        // Transform to number and add number-specific validations
        validator = baseValidator
          .transform((val) => val === '' ? undefined : Number(val))
          .refine(
            (val) => val === undefined || !isNaN(val as number),
            `${field.label || field.name} must be a valid number`
          );

        // Add min/max validations
        if (field.validation?.min !== undefined) {
          validator = validator.refine(
            (val) => val === undefined || (val as number) >= field.validation!.min!,
            `Must be at least ${field.validation!.min}`
          );
        }

        if (field.validation?.max !== undefined) {
          validator = validator.refine(
            (val) => val === undefined || (val as number) <= field.validation!.max!,
            `Must be at most ${field.validation!.max}`
          );
        }

        break;
      }

      case 'select': {
        // For select fields, we'll use a union validator to handle both strings and numbers
        let baseValidator = z.union([z.string(), z.number()]);

        // Add required validation if needed
        if (field.validation?.required) {
          baseValidator = baseValidator.refine(
            (val) => val !== null && val !== undefined && val !== '',
            `${field.label || field.name} is required`
          );
        }

        // Then add the refinement for options
        if (field.options && field.options.length > 0) {
          // Check if options is an array of objects or strings
          if (typeof field.options[0] === 'object' && 'value' in field.options[0]) {
            // It's an array of objects with value property
            const optionValues = (field.options as { value: any }[]).map(opt => opt.value);
            validator = baseValidator.refine(
              (val) => !val || optionValues.includes(val),
              `Must be one of the available options`
            );
          } else {
            // It's an array of strings or numbers
            validator = baseValidator.refine(
              (val) => !val || (field.options as any[]).includes(val),
              `Must be one of the available options`
            );
          }
        } else {
          validator = baseValidator;
        }
        break;
      }

      case 'date': {
        // For date fields, we'll use a date validator with proper transformation
        // This handles both Date objects from the Calendar component
        // and string dates that might come from API or defaultValues
        let dateValidator;

        if (field.validation?.required) {
          dateValidator = z.date({
            invalid_type_error: `${field.label || field.name} must be a valid date`,
            required_error: `${field.label || field.name} is required`
          });
        } else {
          dateValidator = z.date({
            invalid_type_error: `${field.label || field.name} must be a valid date`
          }).optional();
        }

        // Add date restriction validation if specified
        if (field.dateRestriction) {
          const today = new Date();
          today.setHours(0, 0, 0, 0); // Reset time to start of day

          const range = field.dateRestriction.range;
          const includeToday = field.dateRestriction.includeToday !== undefined
            ? field.dateRestriction.includeToday
            : range === 'all';

          if (range === 'past') {
            if (includeToday) {
              // For past dates including today
              dateValidator = dateValidator.refine(
                (date) => !date || date <= today,
                `${field.label || field.name} must be a date in the past or today`
              );
            } else {
              // For past dates excluding today
              dateValidator = dateValidator.refine(
                (date) => !date || date < today,
                `${field.label || field.name} must be a date in the past`
              );
            }
          } else if (range === 'future') {
            if (includeToday) {
              // For future dates including today
              dateValidator = dateValidator.refine(
                (date) => !date || date >= today,
                `${field.label || field.name} must be a date in the future or today`
              );
            } else {
              // For future dates excluding today
              dateValidator = dateValidator.refine(
                (date) => !date || date > today,
                `${field.label || field.name} must be a date in the future`
              );
            }
          }
        }

        // Apply preprocessing to handle different input formats
        validator = z.preprocess(
          (val) => {
            // If it's null or undefined, return undefined
            if (val === null || val === undefined) return undefined;
            // If it's already a Date object, return it
            if (val instanceof Date) return val;
            // If it's a string, try to convert it to a Date
            if (typeof val === 'string') {
              if (val === '') return undefined;
              const date = new Date(val);
              return isNaN(date.getTime()) ? undefined : date;
            }
            // Otherwise return undefined
            return undefined;
          },
          dateValidator
        );

        break;
      }

      case 'image': {
        // For image fields, we'll use a string validator (for the image path)
        let stringValidator;

        // Add required validation if needed
        if (field.validation?.required) {
          stringValidator = z.string().min(1, `${field.label || field.name} is required`);
        } else {
          stringValidator = z.string().nullable().optional();
        }

        validator = stringValidator;
        break;
      }

      default:
        throw new Error(`Unsupported field type: ${field.type}`);
    }

    // Make field optional if not required and not already handled in the switch cases
    if (!field.validation?.required && field.type !== 'checkbox' &&
        field.type !== 'number' && field.type !== 'date') {
      validator = validator.optional();
    }

    shape[field.name] = validator;
  });

  return z.object(shape);
};

export type { FormSchema, ButtonHandler, ActionSchema };

export const DynamicForm = ({
  schema,
  onSubmit,
  defaultValues,
  className,
  buttonHandlers = {},
  onFieldChange
}: DynamicFormProps) => {
  const zodSchema = useMemo(() => getZodSchema(schema.fields), [schema.fields]);
  const methods = useForm({
    resolver: zodResolver(zodSchema),
    defaultValues,
    mode: 'onTouched',
  });

  // Set up a watcher for field changes if onFieldChange is provided
  useEffect(() => {
    if (onFieldChange) {
      const subscription = methods.watch((value, { name, type }) => {
        if (name && type === 'change') {
          onFieldChange(name, value[name]);
        }
      });
      return () => subscription.unsubscribe();
    }
  }, [methods, onFieldChange]);

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={methods.handleSubmit(onSubmit)}
        className={`dynamic-form ${className || ''}`}
        autoComplete="off"
      >
        <div className="form-fields-container">
          {schema.fields.map(field => {
            const fieldComponent = (() => {
              if (field.type === 'text') {
                return (
                  <RHFInputText
                    key={field.name}
                    name={field.name}
                    label={field.label}
                    placeholder={field.placeholder}
                    icon={field.icon}
                  />
                );
              }
              if (field.type === 'password') {
                return (
                  <RHFPassword
                    key={field.name}
                    name={field.name}
                    label={field.label}
                    placeholder={field.placeholder}
                    icon={field.icon}
                  />
                );
              }
              if (field.type === 'checkbox') {
                return (
                  <RHFCheckbox
                    key={field.name}
                    name={field.name}
                    label={field.label}
                    checked={methods.watch(field.name)}
                  />
                );
              }
              if (field.type === 'number') {
                return (
                  <RHFNumber
                    key={field.name}
                    name={field.name}
                    label={field.label}
                    placeholder={field.placeholder}
                    icon={field.icon}
                    min={field.validation?.min}
                    max={field.validation?.max}
                  />
                );
              }
              if (field.type === 'select' && field.options) {
                return (
                  <RHFSelect
                    key={field.name}
                    name={field.name}
                    label={field.label}
                    placeholder={field.placeholder}
                    icon={field.icon}
                    options={field.options}
                  />
                );
              }
              if (field.type === 'date') {
                return (
                  <RHFCalendar
                    key={field.name}
                    name={field.name}
                    label={field.label}
                    placeholder={field.placeholder}
                    icon={field.icon}
                    dateRestriction={field.dateRestriction}
                  />
                );
              }
              if (field.type === 'image') {
                return (
                  <RHFImageUpload
                    key={field.name}
                    name={field.name}
                    label={field.label}
                    folder={field.folder || 'products'}
                    maxSizeInMB={field.maxSizeInMB || 2}
                    acceptedFileTypes={field.acceptedFileTypes || 'image/*'}
                  />
                );
              }
              // Add more field types as needed
              return null;
            })();

            // Add checkbox-field class for checkbox fields (fallback for browsers without :has() support)
            const wrapperClassName = `form-field-wrapper${field.type === 'checkbox' ? ' checkbox-field' : ''}`;

            return fieldComponent ? (
              <div className={wrapperClassName} key={field.name}>
                {fieldComponent}
              </div>
            ) : null;
          })}
        </div>

        <div className="form-actions">
          {schema.actions.map(action => {
            // Handle submit buttons
            if (action.type === 'submit') {
              return (
                <Button
                  key={action.id}
                  type="submit"
                  className="mt-4"
                  onClick={() => {
                    // If there's a custom handler for this button, call it
                    if (buttonHandlers[action.id]) {
                      // Don't prevent default - let the form submission happen
                      const formData = methods.getValues();
                      buttonHandlers[action.id](formData);
                    }
                  }}
                >
                  {action.label}
                </Button>
              );
            }

            // Handle custom action buttons (non-submit)
            if (action.type === 'button' && buttonHandlers[action.id]) {
              return (
                <Button
                  key={action.id}
                  type="button"
                  variant='outline'
                  className="mt-4"
                  onClick={(e) => {
                    e.preventDefault(); // Prevent form submission
                    const formData = methods.getValues();
                    buttonHandlers[action.id](formData);
                  }}
                >
                  {action.label}
                </Button>
              );
            }

            return null;
          })}
        </div>
      </form>
    </FormProvider>
  );
};
