.file-upload-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.file-upload-label {
  font-weight: 500;
  color: var(--text-color);
  font-size: 14px;
  margin-bottom: 4px;
}

.file-upload-preview {
  width: 200px;
  height: 150px;
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: var(--surface-ground);
}

.file-upload-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.file-upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: var(--text-color-secondary);
  text-align: center;
}

.file-upload-placeholder i {
  font-size: 24px;
  color: var(--text-color-secondary);
}

.file-upload-placeholder span {
  font-size: 12px;
}

.file-upload-placeholder span.delete-message {
  color: var(--red-500);
  font-weight: 500;
}

.file-upload-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background-color: var(--surface-ground);
  border: 1px solid var(--border-color);
  border-radius: 6px;
}

.file-upload-info i {
  color: var(--primary-color);
  font-size: 16px;
}

.file-upload-info .file-size {
  color: var(--text-color-secondary);
  font-size: 12px;
  margin-left: auto;
}

.file-upload-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.file-upload-error {
  color: var(--red-500);
  font-size: 12px;
  margin-top: 4px;
}

/* Override PrimeReact FileUpload styles */
.file-upload-actions .p-fileupload .p-button {
  font-size: 12px;
  padding: 6px 12px;
}

/* Responsive design */
@media (max-width: 768px) {
  .file-upload-preview {
    width: 150px;
    height: 120px;
  }

  .file-upload-actions {
    flex-direction: column;
    align-items: stretch;
  }
}
