import React, { useState, useRef, useEffect } from 'react';
import { FileUpload as PrimeFileUpload } from 'primereact/fileupload';
import Button from '@/components/ui/Button/Button';
import './FileUpload.css';

interface FileUploadProps {
  label?: string;
  initialValue?: File | string | null;
  onChange: (file: File | null) => void;
  maxSizeInMB?: number;
  acceptedFileTypes?: string;
  error?: string;
  showDeleteOption?: boolean; // Whether to show delete option for existing images
}

const FileUpload: React.FC<FileUploadProps> = ({
  label = 'File',
  initialValue,
  onChange,
  maxSizeInMB = 2,
  acceptedFileTypes = 'image/*',
  error,
  showDeleteOption = false,
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [hasExistingImage, setHasExistingImage] = useState<boolean>(false);
  const [isImageDeleted, setIsImageDeleted] = useState<boolean>(false);
  const fileUploadRef = useRef<PrimeFileUpload>(null);

  // Convert MB to bytes
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024;

  useEffect(() => {
    if (initialValue) {
      if (initialValue instanceof File) {
        setSelectedFile(initialValue);
        setHasExistingImage(false);
        setIsImageDeleted(false);
        // Create preview URL for image files
        if (initialValue.type.startsWith('image/')) {
          const url = URL.createObjectURL(initialValue);
          setPreviewUrl(url);
          return () => URL.revokeObjectURL(url);
        }
      } else if (typeof initialValue === 'string' && initialValue.trim() !== '') {
        // If it's a string (existing image URL), show it as preview
        setPreviewUrl(initialValue);
        setHasExistingImage(true);
        setIsImageDeleted(false);
        setSelectedFile(null);
      }
    } else {
      setSelectedFile(null);
      setPreviewUrl(null);
      setHasExistingImage(false);
      setIsImageDeleted(false);
    }
  }, [initialValue]);

  const handleFileSelect = (event: { files: File[] }) => {
    const file = event.files[0];

    if (!file) return;

    // Validate file size
    if (file.size > maxSizeInBytes) {
      alert(`File size exceeds the maximum allowed size of ${maxSizeInMB}MB.`);
      return;
    }

    setSelectedFile(file);
    setIsImageDeleted(false);
    onChange(file);

    // Create preview URL for image files
    if (file.type.startsWith('image/')) {
      if (previewUrl && previewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(previewUrl);
      }
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }

    // Clear the file input
    if (fileUploadRef.current) {
      fileUploadRef.current.clear();
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    onChange(null);

    if (previewUrl && previewUrl.startsWith('blob:')) {
      URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl(null);
    setIsImageDeleted(false);
  };

  const handleDeleteExistingImage = () => {
    setIsImageDeleted(true);
    setPreviewUrl(null);
    setSelectedFile(null);
    onChange(null); // This will signal to the backend to delete the existing image
  };

  const isImage = acceptedFileTypes.includes('image');

  return (
    <div className="file-upload-container">
      {label && <label className="file-upload-label">{label}</label>}

      {isImage && (
        <div className="file-upload-preview">
          {previewUrl && !isImageDeleted ? (
            <img src={previewUrl} alt="Preview" />
          ) : (
            <div className="file-upload-placeholder">
              <i className="pi pi-image"></i>
              <span className={isImageDeleted ? "delete-message" : ""}>
                {isImageDeleted ? "Image will be deleted" : "No image selected"}
              </span>
            </div>
          )}
        </div>
      )}

      {!isImage && selectedFile && (
        <div className="file-upload-info">
          <i className="pi pi-file"></i>
          <span>{selectedFile.name}</span>
          <span className="file-size">({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)</span>
        </div>
      )}

      <div className="file-upload-actions">
        <PrimeFileUpload
          ref={fileUploadRef}
          mode="basic"
          name="file"
          url="/api/dummy-url" // This is not used as we handle the upload manually
          accept={acceptedFileTypes}
          maxFileSize={maxSizeInBytes}
          auto={true}
          chooseLabel={selectedFile ? "Change File" : "Select File"}
          onSelect={handleFileSelect}
          className="p-button-sm"
        />

        {selectedFile && (
          <Button
            variant="danger"
            size="small"
            onClick={handleRemoveFile}
            leftIcon={<i className="pi pi-trash"></i>}
          >
            Remove
          </Button>
        )}

        {showDeleteOption && hasExistingImage && !selectedFile && !isImageDeleted && (
          <Button
            variant="danger"
            size="small"
            onClick={handleDeleteExistingImage}
            leftIcon={<i className="pi pi-trash"></i>}
          >
            Delete Image
          </Button>
        )}
      </div>

      {error && <div className="file-upload-error">{error}</div>}
    </div>
  );
};

export default FileUpload;
