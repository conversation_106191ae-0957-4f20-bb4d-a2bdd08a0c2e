import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { DynamicForm } from '@/components/form/DynamicForm';
import Card from '@/components/ui/Card/Card';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';

import productFormSchemaJson from '@/formSchemas/productForm.json';
import type { FormSchema } from '@/components/form/DynamicForm';
import { useCategories, useSubcategoriesByCategory, useCreateProduct } from '@/hooks/useCatalog';
import { CreateProductRequest } from '@/types/catalog.types';
import { organizationItemCatalogRoute } from '@/routes/private/organizationItemCatalog.route';
import './AddProduct.css';

const AddProduct: React.FC = () => {
  const navigate = useNavigate();
  const toast = useRef<ToastRef>(null);

  // State for form schema
  const [productFormSchema, setProductFormSchema] = useState<FormSchema>(productFormSchemaJson as FormSchema);

  // Fetch categories
  const { data: categoriesData } = useCategories();
  const categories = categoriesData?.data || [];

  // State for selected category
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);

  // Fetch subcategories based on selected category
  const { data: subcategoriesData } = useSubcategoriesByCategory(selectedCategoryId);
  const subcategories = subcategoriesData?.data || [];

  // Create product mutation
  const createProductMutation = useCreateProduct();

  // Update form schema with categories and subcategories
  useEffect(() => {
    const updatedSchema = { ...productFormSchema };

    // Update category options
    const categoryField = updatedSchema.fields.find(field => field.name === 'categoryId');
    if (categoryField) {
      categoryField.options = categories.map(category => ({
        label: category.name,
        value: category.id,
      }));
    }

    // Update subcategory options
    const subcategoryField = updatedSchema.fields.find(field => field.name === 'subCategoryId');
    if (subcategoryField) {
      subcategoryField.options = subcategories.map(subcategory => ({
        label: subcategory.name,
        value: subcategory.id,
      }));
    }

    setProductFormSchema(updatedSchema);
  }, [categories, subcategories]);

  // Handle category change
  const handleCategoryChange = (categoryId: number) => {
    setSelectedCategoryId(categoryId);
  };

  // Handle form field changes
  const handleFormFieldChange = (field: string, value: any) => {
    if (field === 'categoryId') {
      handleCategoryChange(value);
    }
  };

  // Handle form submission
  const handleSubmit = async (data: any) => {
    try {
      // Prepare product data
      const productData: CreateProductRequest = {
        name: data.name,
        description: data.description,
        price: parseFloat(data.price),
        categoryId: typeof data.categoryId === 'string' ? parseInt(data.categoryId) : data.categoryId,
        subCategoryId: typeof data.subCategoryId === 'string' ? parseInt(data.subCategoryId) : data.subCategoryId,
        organizationId: 40928446087168, // Use the test organization ID
        imagePaths: data.imagePaths || null,
      };

      // Create product
      await createProductMutation.mutateAsync(productData);

      // Show success toast and navigate back
      toast.current?.showSuccess('Product created successfully');
      navigate({ to: organizationItemCatalogRoute.to });
    } catch (error) {
      console.error('Error creating product:', error);
      toast.current?.showError('Failed to create product');
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate({ to: organizationItemCatalogRoute.to });
  };

  // Get default values for the form
  const getDefaultValues = () => {
    return {
      categoryId: null,
      subCategoryId: null
    };
  };

  return (
    <div className="add-product p-4">
      <Toast ref={toast} position="top-right" />

      <Card
        title="Add Product"
        subtitle="Enter product details"
        variant="elevated"
        padding="large"
        className="max-w-3xl mx-auto"
      >
        <DynamicForm
          schema={productFormSchema}
          onSubmit={handleSubmit}
          defaultValues={getDefaultValues()}
          className="mt-4"
          buttonHandlers={{
            cancel: handleCancel
          }}
          onFieldChange={handleFormFieldChange}
        />
      </Card>
    </div>
  );
};

export default AddProduct;
