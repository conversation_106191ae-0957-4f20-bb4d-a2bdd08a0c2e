import React, { useState, useRef, useEffect } from 'react';
import { useNavigate, useSearch } from '@tanstack/react-router';
import { editCategoryRoute } from '@/routes/private/editCategory.route';
import { DynamicForm } from '@/components/form/DynamicForm';
import Card from '@/components/ui/Card/Card';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';

import categoryFormSchemaJson from '@/formSchemas/categoryForm.json';
import type { FormSchema } from '@/components/form/DynamicForm';
import { useCategory, useUpdateCategory } from '@/hooks/useCatalog';
import { UpdateCategoryRequest } from '@/types/catalog.types';
import { organizationItemCatalogRoute } from '@/routes/private/organizationItemCatalog.route';
import './EditCategory.css';

const EditCategory: React.FC = () => {
  const navigate = useNavigate();
  // Type assertion to handle the search parameters
  const search = useSearch({ from: editCategoryRoute.id }) as { id: string };
  const id = parseInt(search.id);
  const toast = useRef<ToastRef>(null);

  // State for form schema
  const [categoryFormSchema] = useState<FormSchema>(categoryFormSchemaJson as FormSchema);

  // Validate id parameter on component mount
  useEffect(() => {
    if (!search.id || isNaN(id)) {
      toast.current?.showError('Valid Category ID is required');
      navigate({ to: organizationItemCatalogRoute.to });
    }
  }, [search.id, id, navigate]);

  // Fetch category data
  const { data: categoryData, isLoading: isCategoryLoading, isError: isCategoryError } = useCategory(id);
  const category = categoryData?.data;

  // Handle error in fetching category data
  useEffect(() => {
    if (isCategoryError) {
      toast.current?.showError('Failed to fetch category data');
      navigate({ to: organizationItemCatalogRoute.to });
    }
  }, [isCategoryError, navigate]);

  // Update category mutation
  const updateCategoryMutation = useUpdateCategory();

  // Handle form submission
  const handleSubmit = async (data: any) => {
    try {
      const categoryData: UpdateCategoryRequest = {
        name: data.name,
        description: data.description,
        organizationId: 40928446087168, // Use the test organization ID
        imagePath: data.imagePath || null
      };

      await updateCategoryMutation.mutateAsync({ id, data: categoryData });

      // Show success toast and navigate back
      toast.current?.showSuccess('Category updated successfully');
      navigate({ to: organizationItemCatalogRoute.to });
    } catch (error) {
      console.error('Error updating category:', error);
      toast.current?.showError('Failed to update category');
    }
  };

  // Handle cancel button
  const handleCancel = () => {
    navigate({ to: organizationItemCatalogRoute.to });
  };

  // Get default values for form
  const getDefaultValues = () => {
    if (!category) return {};

    return {
      name: category.name,
      description: category.description || '',
      imagePath: category.imagePath || ''
    };
  };

  return (
    <div className="edit-category p-4">
      <Toast ref={toast} position="top-right" />

      <Card
        title="Edit Category"
        subtitle="Update category details"
        variant="elevated"
        padding="large"
        className="max-w-3xl mx-auto"
      >
        {isCategoryLoading ? (
          <div className="loading-indicator">Loading category data...</div>
        ) : (
          <DynamicForm
            schema={categoryFormSchema}
            onSubmit={handleSubmit}
            defaultValues={getDefaultValues()}
            className="mt-4"
            buttonHandlers={{
              cancel: handleCancel
            }}
          />
        )}
      </Card>
    </div>
  );
};

export default EditCategory;
