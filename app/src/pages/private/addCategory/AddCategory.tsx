import React, { useState, useRef } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { DynamicForm } from '@/components/form/DynamicForm';
import Card from '@/components/ui/Card/Card';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import categoryFormSchemaJson from '@/formSchemas/categoryForm.json';
import type { FormSchema } from '@/components/form/DynamicForm';
import { useCreateCategory } from '@/hooks/useCatalog';
import { CreateCategoryRequest } from '@/types/catalog.types';
import { organizationItemCatalogRoute } from '@/routes/private/organizationItemCatalog.route';
import './AddCategory.css';

const AddCategory: React.FC = () => {
  const navigate = useNavigate();
  const toast = useRef<ToastRef>(null);

  // State for form schema
  const [categoryFormSchema, setCategoryFormSchema] = useState<FormSchema>(categoryFormSchemaJson as FormSchema);

  // Create category mutation
  const createCategoryMutation = useCreateCategory();

  // Handle form submission
  const handleSubmit = async (data: any) => {
    try {
      const categoryData: CreateCategoryRequest = {
        name: data.name,
        description: data.description,
        organizationId: 40928446087168, // Use the test organization ID
        imagePath: data.imagePath || null
      };

      await createCategoryMutation.mutateAsync(categoryData);

      // Show success toast and navigate back
      toast.current?.showSuccess('Category created successfully');
      navigate({ to: organizationItemCatalogRoute.to });
    } catch (error) {
      console.error('Error creating category:', error);
      toast.current?.showError('Failed to create category');
    }
  };

  // Handle cancel button
  const handleCancel = () => {
    navigate({ to: organizationItemCatalogRoute.to });
  };



  return (
    <div className="add-category p-4">
      <Toast ref={toast} position="top-right" />

      <Card
        title="Add Category"
        subtitle="Enter category details"
        variant="elevated"
        padding="large"
        className="max-w-3xl mx-auto"
      >
        <DynamicForm
          schema={categoryFormSchema}
          onSubmit={handleSubmit}
          className="mt-4"
          buttonHandlers={{
            cancel: handleCancel
          }}
        />
      </Card>
    </div>
  );
};

export default AddCategory;
