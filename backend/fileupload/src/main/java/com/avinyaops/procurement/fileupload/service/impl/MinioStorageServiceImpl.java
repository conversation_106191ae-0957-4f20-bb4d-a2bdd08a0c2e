package com.avinyaops.procurement.fileupload.service.impl;

import com.avinyaops.fileupload.dto.UploadFileResponseDto;
import com.avinyaops.fileupload.service.FileStorageService;
import com.avinyaops.procurement.fileupload.exception.FileNotFoundException;
import com.avinyaops.procurement.fileupload.exception.FileUploadException;
import com.avinyaops.procurement.fileupload.model.FileMetadata;
import com.avinyaops.procurement.fileupload.repository.FileMetadataRepository;
import com.avinyaops.procurement.fileupload.util.FileTypeValidator;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.RemoveObjectArgs;
import io.minio.GetPresignedObjectUrlArgs;
import io.minio.http.Method;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

// TODO: make UUID snoflake id later
@Slf4j
@Service
@RequiredArgsConstructor
public class MinioStorageServiceImpl implements FileStorageService {
    private final MinioClient minioClient;
    private final FileMetadataRepository fileMetadataRepository;

    @Value("${minio.bucket}")
    private String bucket;

    @Value("${minio.presigned-url-expiry-minutes:15}")
    private int presignedUrlExpiryMinutes;

    @Override
    @Transactional
    public UploadFileResponseDto uploadFile(MultipartFile file, String folderPath) {
        // Validate file type
        FileTypeValidator.validateFileType(file);
        try {
            String fileId = UUID.randomUUID().toString();
            String fileName = file.getOriginalFilename();
            String contentType = file.getContentType();
            long size = file.getSize();

            // Construct the object name with folder path
            String objectName = folderPath + "/" + fileId + "/" + fileName;

            // Build metadata
            FileMetadata metadata = FileMetadata.builder()
                    .id(UUID.fromString(fileId))
                    .fileName(fileName)
                    .contentType(contentType)
                    .size(size)
                    .uploadTime(LocalDateTime.now())
                    .storageKey(objectName)
                    .build();

            // Upload file
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucket)
                            .object(objectName)
                            .stream(file.getInputStream(), size, -1)
                            .contentType(contentType)
                            .userMetadata(metadata.toMap())
                            .build());

            // Save metadata
            fileMetadataRepository.save(metadata);

            return UploadFileResponseDto.builder()
                    .fileId(fileId)
                    .fileName(fileName)
                    .contentType(contentType)
                    .size(size)
                    .storageKey(objectName)
                    .build();
        } catch (FileNotFoundException fnfex) {
            throw fnfex;
        } catch (Exception e) {
            throw new FileUploadException("Failed to upload file: ", file.getOriginalFilename(), e);
        }
    }

    @Override
    @Transactional
    public List<UploadFileResponseDto> uploadFiles(List<MultipartFile> files, String folderPath) {
        // Validate file types
        for (MultipartFile file : files) {
            FileTypeValidator.validateFileType(file);
        }

        List<FileMetadata> metadataList = new ArrayList<>();
        List<CompletableFuture<Void>> uploadFutures = new ArrayList<>();
        List<UploadFileResponseDto> responseDtos = new ArrayList<>();
        Set<String> successfullyUploadedObjectNames = ConcurrentHashMap.newKeySet();
        AtomicBoolean anyFailure = new AtomicBoolean(false);

        try {
            for (MultipartFile file : files) {
                String fileId = UUID.randomUUID().toString();
                String fileName = file.getOriginalFilename();
                String contentType = file.getContentType();
                long size = file.getSize();

                // Construct the object name with folder path
                String objectName = folderPath + "/" + fileId + "/" + fileName;

                // Save metadata
                FileMetadata metadata = FileMetadata.builder()
                        .id(UUID.fromString(fileId))
                        .fileName(fileName)
                        .contentType(contentType)
                        .size(size)
                        .uploadTime(LocalDateTime.now())
                        .storageKey(objectName)
                        .build();
                metadataList.add(metadata);

                responseDtos.add(UploadFileResponseDto.builder()
                        .fileId(fileId)
                        .fileName(fileName)
                        .contentType(contentType)
                        .size(size)
                        .storageKey(objectName)
                        .build());

                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        minioClient.putObject(
                                PutObjectArgs.builder()
                                        .bucket(bucket)
                                        .object(objectName)
                                        .stream(file.getInputStream(), size, -1)
                                        .contentType(contentType)
                                        .userMetadata(metadata.toMap())
                                        .build());

                        successfullyUploadedObjectNames.add(objectName);
                    } catch (Exception e) {
                        anyFailure.set(true);
                    }
                });
                uploadFutures.add(future);
            }

        } catch (Exception e) {
            log.error("Failed batch upload: ", e);
            anyFailure.set(true);
        }

        CompletableFuture.allOf(uploadFutures.toArray(new CompletableFuture[0])).join();

        if (anyFailure.get()) {
            cleanupMinioObjects(successfullyUploadedObjectNames);

            throw new FileUploadException("Failed to upload one or more files in the batch", null);
        }

        fileMetadataRepository.saveAll(metadataList);

        return responseDtos;
    }

    @Override
    public String generateViewUrl(String fileId) {
        try {
            if(fileId == null || fileId.isBlank()){
                throw new FileNotFoundException("null or empty fileId");
            }
            FileMetadata metadata = getFileMetadata(fileId);
            return minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .method(Method.GET)
                            .bucket(bucket)
                            .object(metadata.getStorageKey())
                            .expiry(presignedUrlExpiryMinutes, TimeUnit.MINUTES)
                            .build());
        } catch (FileNotFoundException fnfex) {
            throw fnfex;
        } catch (Exception e) {
            throw new FileUploadException("Failed to generate view URL for file ID: ", fileId, e);
        }
    }

    @Override
    @Transactional
    public String deleteFile(String fileId) {
        try {
            if (fileId == null || fileId.isBlank()) {
                throw new FileNotFoundException("null or empty fileId");
            }
            FileMetadata metadata = getFileMetadata(fileId);
            String fileStorageKey = metadata.getStorageKey();

            minioClient.removeObject(
                    RemoveObjectArgs.builder()
                            .bucket(bucket)
                            .object(fileStorageKey)
                            .build());
            fileMetadataRepository.deleteById(metadata.getId());
            return fileStorageKey;
        } catch (FileNotFoundException fnfex) {
            throw fnfex;
        } catch (Exception e) {
            throw new FileUploadException("Failed to delete file for file ID: ", fileId, e);
        }
    }

    private FileMetadata getFileMetadata(String fileId) {
        return fileMetadataRepository.findById(UUID.fromString(fileId))
                .orElseThrow(() -> new FileNotFoundException(fileId));
    }

    private void cleanupMinioObjects(Set<String> objectNames) {
        if (objectNames.isEmpty()) {
            return;
        }

        log.info("Batch upload failed, Cleaning up {} objects from MinIO", objectNames.size());
        List<CompletableFuture<Void>> deleteFutures = new ArrayList<>();

        for (String objectName : objectNames) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    minioClient.removeObject(
                            RemoveObjectArgs.builder()
                                    .bucket(bucket)
                                    .object(objectName)
                                    .build());
                    log.info("Cleaned up object from MinIO: {}", objectName);
                } catch (Exception ex) {
                    log.warn("Failed to clean up object from MinIO: {}", objectName, ex);
                }
            });
            deleteFutures.add(future);
        }
        CompletableFuture.allOf(deleteFutures.toArray(new CompletableFuture[0])).join();
    }
}