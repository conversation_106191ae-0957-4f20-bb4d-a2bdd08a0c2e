package com.avinyaops.procurement.product.service;

import com.avinyaops.procurement.product.dto.ProductDTO;
import com.avinyaops.procurement.product.exception.ResourceAlreadyExistsException;
import com.avinyaops.procurement.product.exception.ResourceNotFoundException;
import com.avinyaops.procurement.product.model.Product;
import com.avinyaops.procurement.product.repository.ProductRepository;
import com.avinyaops.procurement.product.category.model.ProductCategory;
import com.avinyaops.procurement.product.category.model.ProductSubCategory;
import com.avinyaops.procurement.product.category.repository.ProductCategoryRepository;
import com.avinyaops.procurement.product.category.repository.ProductSubCategoryRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class ProductServiceImpl implements ProductService {

    private final ProductRepository productRepository;
    private final ProductCategoryRepository categoryRepository;
    private final ProductSubCategoryRepository subCategoryRepository;

    @Override
    public ProductDTO createProduct(ProductDTO productDTO) {
        // Check if product with same name exists for the organization
        if (productRepository.existsByNameAndOrganizationId(productDTO.getName(), productDTO.getOrganizationId())) {
            throw new ResourceAlreadyExistsException("Product with name " + productDTO.getName() + " already exists");
        }

        // Validate category and sub-category
        ProductCategory category = categoryRepository.findById(productDTO.getCategoryId())
                .orElseThrow(() -> new ResourceNotFoundException("Category not found with id: " + productDTO.getCategoryId()));

        ProductSubCategory subCategory = null;
        if (productDTO.getSubCategoryId() != null) {
            subCategory = subCategoryRepository.findById(productDTO.getSubCategoryId())
                    .orElseThrow(() -> new ResourceNotFoundException("Sub-category not found with id: " + productDTO.getSubCategoryId()));
            
            // Validate sub-category belongs to the category
            if (!subCategory.getCategory().getId().equals(category.getId())) {
                throw new IllegalArgumentException("Sub-category does not belong to the specified category");
            }
        }

        Product product = Product.builder()
                .name(productDTO.getName())
                .description(productDTO.getDescription())
                .price(productDTO.getPrice())
                .category(category)
                .subCategory(subCategory)
                .organizationId(productDTO.getOrganizationId())
                .build();

        return mapToDTO(productRepository.save(product));
    }

    @Override
    public ProductDTO updateProduct(Long id, ProductDTO productDTO) {
        Product product = productRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Product not found with id: " + id));

        // Check if another product with same name exists for the organization
        if (!product.getName().equals(productDTO.getName()) &&
            productRepository.existsByNameAndOrganizationId(productDTO.getName(), productDTO.getOrganizationId())) {
            throw new ResourceAlreadyExistsException("Product with name " + productDTO.getName() + " already exists");
        }

        // Validate category and sub-category
        ProductCategory category = categoryRepository.findById(productDTO.getCategoryId())
                .orElseThrow(() -> new ResourceNotFoundException("Category not found with id: " + productDTO.getCategoryId()));

        ProductSubCategory subCategory = null;
        if (productDTO.getSubCategoryId() != null) {
            subCategory = subCategoryRepository.findById(productDTO.getSubCategoryId())
                    .orElseThrow(() -> new ResourceNotFoundException("Sub-category not found with id: " + productDTO.getSubCategoryId()));
            
            // Validate sub-category belongs to the category
            if (!subCategory.getCategory().getId().equals(category.getId())) {
                throw new IllegalArgumentException("Sub-category does not belong to the specified category");
            }
        }

        product.setName(productDTO.getName());
        product.setDescription(productDTO.getDescription());
        product.setPrice(productDTO.getPrice());
        product.setCategory(category);
        product.setSubCategory(subCategory);
        product.setOrganizationId(productDTO.getOrganizationId());

        return mapToDTO(productRepository.save(product));
    }

    @Override
    public void deleteProduct(Long id) {
        Product product = productRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Product not found with id: " + id));
        product.softDelete();
        productRepository.save(product);
    }

    @Override
    public ProductDTO getProduct(Long id) {
        return productRepository.findById(id)
                .map(this::mapToDTO)
                .orElseThrow(() -> new ResourceNotFoundException("Product not found with id: " + id));
    }

    @Override
    public List<ProductDTO> getAllProducts(Long organizationId) {
        if (organizationId != null) {
            return productRepository.findAllByOrganizationIdOrOrganizationIdIsNull(organizationId)
                    .stream()
                    .map(this::mapToDTO)
                    .collect(Collectors.toList());
        }
        return productRepository.findAllByOrganizationIdIsNull()
                .stream()
                .map(this::mapToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ProductDTO> getProductsByCategory(Long categoryId, Long organizationId) {
        if (organizationId != null) {
            return productRepository.findAllByCategoryIdAndOrganizationIdOrCategoryIdAndOrganizationIdIsNull(categoryId, organizationId)
                    .stream()
                    .map(this::mapToDTO)
                    .collect(Collectors.toList());
        }
        return productRepository.findAllByCategoryIdAndOrganizationIdIsNull(categoryId)
                .stream()
                .map(this::mapToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ProductDTO> getProductsBySubCategory(Long subCategoryId, Long organizationId) {
        if (organizationId != null) {
            return productRepository.findAllBySubCategoryIdAndOrganizationIdOrSubCategoryIdAndOrganizationIdIsNull(subCategoryId, organizationId)
                    .stream()
                    .map(this::mapToDTO)
                    .collect(Collectors.toList());
        }
        return productRepository.findAllBySubCategoryIdAndOrganizationIdIsNull(subCategoryId)
                .stream()
                .map(this::mapToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ProductDTO> searchProducts(String query, Long organizationId) {
        if (organizationId != null) {
            return productRepository.searchByNameOrDescriptionAndOrganizationIdOrOrganizationIdIsNull(query, organizationId)
                    .stream()
                    .map(this::mapToDTO)
                    .collect(Collectors.toList());
        }
        return productRepository.searchByNameOrDescriptionAndOrganizationIdIsNull(query)
                .stream()
                .map(this::mapToDTO)
                .collect(Collectors.toList());
    }

    private ProductDTO mapToDTO(Product product) {
        return ProductDTO.builder()
                .id(product.getId())
                .name(product.getName())
                .description(product.getDescription())
                .price(product.getPrice())
                .categoryId(product.getCategory().getId())
                .subCategoryId(product.getSubCategory() != null ? product.getSubCategory().getId() : null)
                .organizationId(product.getOrganizationId())
                .build();
    }
} 