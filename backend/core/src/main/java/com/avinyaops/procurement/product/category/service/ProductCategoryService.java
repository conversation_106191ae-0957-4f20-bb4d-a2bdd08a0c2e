package com.avinyaops.procurement.product.category.service;

import com.avinyaops.procurement.product.category.dto.ProductCategoryRequestDTO;
import com.avinyaops.procurement.product.category.dto.ProductCategoryResponseDTO;
import com.avinyaops.procurement.product.category.dto.ProductSubCategoryDTO;

import java.util.List;

public interface ProductCategoryService {
    ProductCategoryResponseDTO createCategory(ProductCategoryRequestDTO categoryRequestDTO);
    
    ProductCategoryResponseDTO updateCategory(Long id, ProductCategoryRequestDTO categoryRequestDTO);
    
    void deleteCategory(Long id);
    
    ProductCategoryResponseDTO getCategory(Long id);
    
    List<ProductCategoryResponseDTO> getAllCategories(Long organizationId);
    
    ProductSubCategoryDTO createSubCategory(Long categoryId, ProductSubCategoryDTO subCategoryDTO);
    
    ProductSubCategoryDTO updateSubCategory(Long categoryId, Long subCategoryId, ProductSubCategoryDTO subCategoryDTO);
    
    void deleteSubCategory(Long categoryId, Long subCategoryId);
    
    ProductSubCategoryDTO getSubCategory(Long categoryId, Long subCategoryId);
    
    List<ProductSubCategoryDTO> getAllSubCategoriesByCategoryId(Long categoryId);
    
    List<ProductSubCategoryDTO> getAllSubCategories(Long organizationId);
} 