package com.avinyaops.procurement.product.service;

import com.avinyaops.procurement.product.dto.ProductDTO;
import java.util.List;

public interface ProductService {
    ProductDTO createProduct(ProductDTO productDTO);
    
    ProductDTO updateProduct(Long id, ProductDTO productDTO);
    
    void deleteProduct(Long id);
    
    ProductDTO getProduct(Long id);
    
    List<ProductDTO> getAllProducts(Long organizationId);
    
    List<ProductDTO> getProductsByCategory(Long categoryId, Long organizationId);
    
    List<ProductDTO> getProductsBySubCategory(Long subCategoryId, Long organizationId);
    
    List<ProductDTO> searchProducts(String query, Long organizationId);
} 