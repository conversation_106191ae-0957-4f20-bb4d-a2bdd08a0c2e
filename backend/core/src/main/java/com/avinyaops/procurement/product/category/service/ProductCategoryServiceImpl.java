package com.avinyaops.procurement.product.category.service;

import com.avinyaops.fileupload.service.FileStorageService;
import com.avinyaops.procurement.product.category.dto.ProductCategoryRequestDTO;
import com.avinyaops.procurement.product.category.dto.ProductCategoryResponseDTO;
import com.avinyaops.procurement.product.category.dto.ProductSubCategoryDTO;
import com.avinyaops.procurement.product.category.model.ProductCategory;
import com.avinyaops.procurement.product.category.model.ProductSubCategory;
import com.avinyaops.procurement.product.category.repository.ProductCategoryRepository;
import com.avinyaops.procurement.product.category.repository.ProductSubCategoryRepository;
import com.avinyaops.procurement.product.exception.ResourceAlreadyExistsException;
import com.avinyaops.procurement.product.exception.ResourceNotFoundException;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class ProductCategoryServiceImpl implements ProductCategoryService {
    private final ProductCategoryRepository categoryRepository;
    private final ProductSubCategoryRepository subCategoryRepository;

    private final FileStorageService fileStorageService;
    private final String categoryImageFolderPath = "category";
    private final String subCategoryImageFolderPath = "subcategory";

    @Override
    @Transactional
    public ProductCategoryResponseDTO createCategory(ProductCategoryRequestDTO categoryRequestDTO) {
        // Check if category with same name exists in the organization or globally
        if (categoryRepository.existsByNameAndOrganizationId(categoryRequestDTO.getName(),
                categoryRequestDTO.getOrganizationId())) {
            throw new ResourceAlreadyExistsException("Category with this name already exists");
        }

        ProductCategory category = ProductCategory.builder()
                .name(categoryRequestDTO.getName())
                .description(categoryRequestDTO.getDescription())
                .organizationId(categoryRequestDTO.getOrganizationId())
                .imageFileId(fileStorageService.uploadFile(categoryRequestDTO.getImageFile(), categoryImageFolderPath)
                        .getFileId())
                .build();

        category = categoryRepository.save(category);
        return toCategoryResponseDTO(category);
    }

    @Override
    @Transactional
    public ProductCategoryResponseDTO updateCategory(Long id, ProductCategoryRequestDTO categoryDTO) {
        ProductCategory category = categoryRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Category not found"));

        if (!category.getName().equals(categoryDTO.getName()) && 
            categoryRepository.existsByNameAndOrganizationId(categoryDTO.getName(), categoryDTO.getOrganizationId())) {
            throw new ResourceAlreadyExistsException("Category with this name already exists");
        }

        category.setName(categoryDTO.getName());
        category.setDescription(categoryDTO.getDescription());
        category.setOrganizationId(categoryDTO.getOrganizationId());
        category = categoryRepository.save(category);
        return toCategoryResponseDTO(category);
    }

    @Override
    @Transactional
    public void deleteCategory(Long id) {
        ProductCategory category = categoryRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Category not found"));
        // TODO: cascade below and delete all related sub categories and cascade down to delete all related products
        category.softDelete();
        categoryRepository.save(category);
    }

    @Override
    @Transactional(readOnly = true)
    public ProductCategoryResponseDTO getCategory(Long id) {
        ProductCategory category = categoryRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Category not found"));
        return toCategoryResponseDTO(category);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ProductCategoryResponseDTO> getAllCategories(Long organizationId) {
        if (organizationId != null) {
            // Return categories for the organization and global categories (where organizationId is null)
            return categoryRepository.findAllByOrganizationIdOrOrganizationIdIsNull(organizationId).stream()
                    .map(this::toCategoryResponseDTO)
                    .collect(Collectors.toList());
        } else {
            // Return only global categories
            log.info("Getting all global categories {}", categoryRepository.findAll());
            return categoryRepository.findAll().stream()
                    .map(this::toCategoryResponseDTO)
                    .collect(Collectors.toList());
        }
    }

    @Override
    public ProductSubCategoryDTO createSubCategory(Long categoryId, ProductSubCategoryDTO subCategoryDTO) {
        ProductCategory category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new ResourceNotFoundException("Category not found"));

        if (subCategoryRepository.existsByNameAndCategoryIdAndOrganizationId(
                subCategoryDTO.getName(), categoryId, subCategoryDTO.getOrganizationId())) {
            throw new ResourceAlreadyExistsException("Sub-category with this name already exists in the category");
        }

        ProductSubCategory subCategory = ProductSubCategory.builder()
                .name(subCategoryDTO.getName())
                .description(subCategoryDTO.getDescription())
                .category(category)
                .organizationId(subCategoryDTO.getOrganizationId())
                .build();

        subCategory = subCategoryRepository.save(subCategory);
        return toSubCategoryDTO(subCategory);
    }

    @Override
    public ProductSubCategoryDTO updateSubCategory(Long categoryId, Long subCategoryId, 
            ProductSubCategoryDTO subCategoryDTO) {
        ProductSubCategory subCategory = subCategoryRepository.findById(subCategoryId)
                .orElseThrow(() -> new ResourceNotFoundException("Sub-category not found"));

        if (!subCategory.getCategory().getId().equals(categoryId)) {
            throw new ResourceNotFoundException("Sub-category does not belong to the specified category");
        }

        if (!subCategory.getName().equals(subCategoryDTO.getName()) && 
            subCategoryRepository.existsByNameAndCategoryIdAndOrganizationId(
                subCategoryDTO.getName(), categoryId, subCategoryDTO.getOrganizationId())) {
            throw new ResourceAlreadyExistsException("Sub-category with this name already exists in the category");
        }

        subCategory.setName(subCategoryDTO.getName());
        subCategory.setDescription(subCategoryDTO.getDescription());
        subCategory.setOrganizationId(subCategoryDTO.getOrganizationId());
        subCategory = subCategoryRepository.save(subCategory);
        return toSubCategoryDTO(subCategory);
    }

    @Override
    public void deleteSubCategory(Long categoryId, Long subCategoryId) {
        ProductSubCategory subCategory = subCategoryRepository.findById(subCategoryId)
                .orElseThrow(() -> new ResourceNotFoundException("Sub-category not found"));

        if (!subCategory.getCategory().getId().equals(categoryId)) {
            throw new ResourceNotFoundException("Sub-category does not belong to the specified category");
        }

        subCategory.softDelete();
        subCategoryRepository.save(subCategory);
    }

    @Override
    public ProductSubCategoryDTO getSubCategory(Long categoryId, Long subCategoryId) {
        ProductSubCategory subCategory = subCategoryRepository.findById(subCategoryId)
                .orElseThrow(() -> new ResourceNotFoundException("Sub-category not found"));

        if (!subCategory.getCategory().getId().equals(categoryId)) {
            throw new ResourceNotFoundException("Sub-category does not belong to the specified category");
        }

        return toSubCategoryDTO(subCategory);
    }

    @Override
    public List<ProductSubCategoryDTO> getAllSubCategoriesByCategoryId(Long categoryId) {
        return subCategoryRepository.findAllByCategoryId(
                categoryId).stream()
                    .map(this::toSubCategoryDTO)
                    .collect(Collectors.toList());
    }

    @Override
    public List<ProductSubCategoryDTO> getAllSubCategories(Long organizationId) {
        return subCategoryRepository.findAllByOrganizationId(
                organizationId).stream()
                .map(this::toSubCategoryDTO)
                .collect(Collectors.toList());

    }

    private ProductCategoryResponseDTO toCategoryResponseDTO(ProductCategory category) {
        return ProductCategoryResponseDTO.builder()
                .id(category.getId())
                .name(category.getName())
                .description(category.getDescription())
                .organizationId(category.getOrganizationId())
                .imageUrl(
                        Optional.ofNullable(category.getImageFileId()).map(fileStorageService::generateViewUrl)
                                .orElse(""))
                .build();
    }

    private ProductSubCategoryDTO toSubCategoryDTO(ProductSubCategory subCategory) {
        return ProductSubCategoryDTO.builder()
                .id(subCategory.getId())
                .name(subCategory.getName())
                .description(subCategory.getDescription())
                .categoryId(subCategory.getCategory().getId())
                .organizationId(subCategory.getOrganizationId())
                .build();
    }
} 